<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="ZMAIL-24小时匿名邮箱 - 创建临时邮箱地址，接收邮件，无需注册，保护您的隐私安全" />
    <meta name="keywords" content="临时邮箱,匿名邮箱,一次性邮箱,隐私保护,电子邮件,ZMAIL" />
    <meta name="author" content="ZMAIL" />
    <meta name="robots" content="index, follow" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://mail.city9.net/" />
    <meta property="og:title" content="ZMAIL-24小时匿名邮箱" />
    <meta property="og:description" content="创建临时邮箱地址，接收邮件，无需注册，保护您的隐私安全" />
    <meta property="og:image" content="/og-image.jpg" />
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://mail.city9.net/" />
    <meta property="twitter:title" content="ZMAIL-24小时匿名邮箱" />
    <meta property="twitter:description" content="创建临时邮箱地址，接收邮件，无需注册，保护您的隐私安全" />
    <meta property="twitter:image" content="/og-image.jpg" />
    
    <!-- 规范链接 -->
    <link rel="canonical" href="https://mail.city9.net/" />
    
    <!-- PWA支持 -->
    <link rel="manifest" href="/manifest.json" />
    <meta name="theme-color" content="#4f46e5" />
    <link rel="apple-touch-icon" href="/icon-192x192.png" />
    
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <title>24時間使い捨てメール</title>

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-ZS8J75RNR3"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-ZS8J75RNR3');
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html> 