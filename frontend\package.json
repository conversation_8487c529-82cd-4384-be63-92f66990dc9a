{"name": "temp-mail-frontend", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "i18next": "^23.4.4", "i18next-browser-languagedetector": "^7.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^13.1.2", "react-router-dom": "^6.15.0", "tailwind-merge": "^1.14.0"}, "devDependencies": {"@types/node": "^20.5.7", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.15", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.4.28", "tailwindcss": "^3.3.3", "tailwindcss-animate": "^1.0.6", "typescript": "^5.0.2", "vite": "^4.4.5"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}