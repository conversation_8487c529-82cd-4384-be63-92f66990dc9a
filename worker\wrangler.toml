name = "zmail-backend"
main = "src/index.ts"

# 打包配置
minify = true # 生产环境压缩代码
compatibility_flags = [ "nodejs_compat" ]
compatibility_date = "2025-03-01"

# 启用 D1 数据库
[[d1_databases]]
binding = "DB"
database_name = "tempmail"
database_id = "ee0fad0c-f649-47b8-b55e-7f06813a39be"  # 需要替换为您的实际数据库 ID

# 配置邮件处理
[triggers]
crons = ["0 * * * *"]  # 每小时运行一次清理任务

[build]
command = "" # 使用默认打包，不需要自定义构建命令

# 默认开启日志，方便调试
[observability]
enabled = false
head_sampling_rate = 1 # optional. default = 1.