<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮件图片显示测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-html {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background-color: #e8f5e8;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>邮件图片显示测试</h1>
    
    <div class="test-section">
        <h2>测试1: 标准cid引用</h2>
        <div class="test-html">原始HTML: &lt;img src="cid:image.png" alt="测试图片"&gt;</div>
        <div class="result" id="test1-result"></div>
    </div>
    
    <div class="test-section">
        <h2>测试2: 带引号的cid引用</h2>
        <div class="test-html">原始HTML: &lt;img src="cid:photo.jpg" alt="照片"&gt;</div>
        <div class="result" id="test2-result"></div>
    </div>
    
    <div class="test-section">
        <h2>测试3: 复杂的邮件HTML</h2>
        <div class="test-html">原始HTML: 
&lt;div&gt;
    &lt;p&gt;这是一封包含图片的邮件：&lt;/p&gt;
    &lt;img src="cid:logo.png" style="width: 100px;"&gt;
    &lt;p&gt;还有另一张图片：&lt;/p&gt;
    &lt;img src="cid:banner.jpg" alt="横幅"&gt;
&lt;/div&gt;</div>
        <div class="result" id="test3-result"></div>
    </div>

    <script>
        // 模拟附件数据
        const mockAttachments = [
            {
                id: 'att-001',
                emailId: 'email-001',
                filename: 'image.png',
                mimeType: 'image/png',
                size: 12345,
                createdAt: Date.now(),
                isLarge: false,
                chunksCount: 0
            },
            {
                id: 'att-002',
                emailId: 'email-001',
                filename: 'photo.jpg',
                mimeType: 'image/jpeg',
                size: 23456,
                createdAt: Date.now(),
                isLarge: false,
                chunksCount: 0
            },
            {
                id: 'att-003',
                emailId: 'email-001',
                filename: 'logo.png',
                mimeType: 'image/png',
                size: 5678,
                createdAt: Date.now(),
                isLarge: false,
                chunksCount: 0
            },
            {
                id: 'att-004',
                emailId: 'email-001',
                filename: 'banner.jpg',
                mimeType: 'image/jpeg',
                size: 34567,
                createdAt: Date.now(),
                isLarge: false,
                chunksCount: 0
            }
        ];

        // 模拟API_BASE_URL
        const API_BASE_URL = 'https://api.example.com';

        // 复制EmailDetail组件中的函数
        const getAttachmentUrl = (attachmentId, download = false) => {
            return `${API_BASE_URL}/api/attachments/${attachmentId}${download ? '?download=true' : ''}`;
        };

        const escapeRegExp = (string) => {
            return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        };

        const processHtmlContent = (htmlContent, attachments) => {
            if (!htmlContent || !attachments.length) {
                return htmlContent;
            }

            let processedHtml = htmlContent;
            
            // 为每个图片类型的附件创建映射
            const imageAttachments = attachments.filter(att => att.mimeType.startsWith('image/'));
            
            if (imageAttachments.length === 0) {
                return htmlContent;
            }

            // 首先处理所有的img标签中的cid引用
            processedHtml = processedHtml.replace(/<img[^>]*>/gi, (imgTag) => {
                // 提取src属性中的cid值
                const srcMatch = imgTag.match(/src=["']([^"']+)["']/i);
                if (!srcMatch) return imgTag;
                
                const srcValue = srcMatch[1];
                
                // 检查是否是cid引用
                if (srcValue.toLowerCase().startsWith('cid:')) {
                    const cidValue = srcValue.substring(4); // 移除 "cid:" 前缀
                    
                    // 尝试匹配附件
                    const matchedAttachment = imageAttachments.find(att => {
                        const filename = att.filename;
                        // 尝试多种匹配方式
                        return cidValue === filename || 
                               cidValue === att.id ||
                               cidValue === filename.toLowerCase() ||
                               cidValue.toLowerCase() === filename.toLowerCase() ||
                               // 处理文件名不带扩展名的情况
                               cidValue === filename.split('.')[0] ||
                               cidValue.toLowerCase() === filename.split('.')[0].toLowerCase();
                    });
                    
                    if (matchedAttachment) {
                        const attachmentUrl = getAttachmentUrl(matchedAttachment.id, true);
                        return imgTag.replace(/src=["'][^"']+["']/, `src="${attachmentUrl}"`);
                    }
                }
                
                return imgTag;
            });

            return processedHtml;
        };

        // 测试用例
        const testCases = [
            {
                id: 'test1-result',
                html: '<img src="cid:image.png" alt="测试图片">'
            },
            {
                id: 'test2-result',
                html: '<img src="cid:photo.jpg" alt="照片">'
            },
            {
                id: 'test3-result',
                html: `<div>
    <p>这是一封包含图片的邮件：</p>
    <img src="cid:logo.png" style="width: 100px;">
    <p>还有另一张图片：</p>
    <img src="cid:banner.jpg" alt="横幅">
</div>`
            }
        ];

        // 运行测试
        testCases.forEach(testCase => {
            const processed = processHtmlContent(testCase.html, mockAttachments);
            const resultElement = document.getElementById(testCase.id);
            resultElement.innerHTML = `<strong>处理后的HTML:</strong><br><pre>${processed}</pre>`;
        });
    </script>
</body>
</html>
