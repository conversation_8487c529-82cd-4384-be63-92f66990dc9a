# 依赖目录
node_modules/
.pnp/
.pnp.js

# 构建输出
dist/
build/
.wrangler/
.cloudflare/
.output/

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.dev.vars

# 日志
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 编辑器目录和文件
.vscode/*
!.vscode/extensions.json
.idea/
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
*.sublime-workspace

# 测试覆盖率
coverage/
.nyc_output/

# 缓存
.npm/
.eslintcache
.stylelintcache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/
.cache/
.parcel-cache/
.next/
.nuxt/
.vuepress/dist
.serverless/
.fusebox/
.dynamodb/
.tern-port
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz

# TypeScript
*.tsbuildinfo

# Vite
.vite/

# Cloudflare Workers
worker-configuration.json
.dev.vars

# 本地数据库
*.sqlite
*.sqlite3
*.db

# 临时文件
tmp/
temp/

# 系统文件
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/ 