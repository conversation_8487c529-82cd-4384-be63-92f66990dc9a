import * as PostalMimeModule from 'postal-mime';
import { Env, ParsedEmail } from './types';
import { getMailbox, saveEmail, saveAttachment, updateEmailHtmlContent } from './database';

const PostalMime = PostalMimeModule.default;

/**
 * 处理接收到的邮件
 * @param message 邮件消息
 * @param env 环境变量
 */
export async function handleEmail(message: any, env: Env): Promise<void> {
  try {
    const parser = new PostalMime();
    const email = await parser.parse(message.raw) as ParsedEmail;

    console.log('邮件解析结果:', {
      subject: email.subject,
      from: email.from,
      to: email.to,
      hasHtml: !!email.html,
      hasText: !!email.text,
      attachmentsCount: email.attachments?.length || 0
    });

    // 提取邮箱地址部分（从email.to获取 ）
    const mailboxAddress = email.to[0].address.split('@')[0];
    
    // 查找对应的邮箱
    const mailbox = await getMailbox(env.DB, mailboxAddress);
    
    if (!mailbox) {
      console.log('邮箱不存在');
      throw new Error('邮箱不存在');
    }

    // 保存邮件
    const savedEmail = await saveEmail(env.DB, {
      mailboxId: mailbox.id,
      fromAddress: email.from.address,
      fromName: email.from.name || '',
      toAddress: mailboxAddress,
      subject: email.subject || '',
      textContent: email.text || '',
      htmlContent: email.html || '',
      hasAttachments: !!email.attachments?.length,
    });

    // 保存附件（如果有）
    const attachmentMap = new Map<string, string>(); // cid -> attachmentId 映射
    if (email.attachments && email.attachments.length > 0) {
      console.log(`开始保存 ${email.attachments.length} 个附件`);

      for (const attachment of email.attachments) {
        try {
          // 将 ArrayBuffer 转换为 Base64 字符串
          const base64Content = arrayBufferToBase64(attachment.content);

          // 计算附件大小（字节）
          const size = attachment.size || attachment.content.byteLength;

          // 保存附件
          const savedAttachment = await saveAttachment(env.DB, {
            emailId: savedEmail.id,
            filename: attachment.filename,
            mimeType: attachment.mimeType,
            content: base64Content,
            size: size,
            contentId: attachment.contentId // 保存Content-ID用于内联图片引用
          });

          // 如果附件有Content-ID，建立映射关系
          if (attachment.contentId) {
            attachmentMap.set(attachment.contentId, savedAttachment.id);
          }

          console.log(`附件 ${attachment.filename} 保存成功`);
        } catch (attachmentError) {
          console.error(`保存附件 ${attachment.filename} 失败:`, attachmentError);
          // 继续处理其他附件，不中断流程
        }
      }
    }

    // 处理HTML内容中的内联图片引用
    if (savedEmail.htmlContent && attachmentMap.size > 0) {
      try {
        const processedHtml = await processInlineImages(savedEmail.htmlContent, attachmentMap);
        // 更新邮件的HTML内容
        await updateEmailHtmlContent(env.DB, savedEmail.id, processedHtml);
        console.log('HTML内容中的内联图片引用已处理');
      } catch (error) {
        console.error('处理内联图片失败:', error);
      }
    }
  } catch (error) {
    console.error('处理邮件失败:', error);
  }
}

/**
 * 处理HTML内容中的内联图片引用
 * @param htmlContent 原始HTML内容
 * @param attachmentMap cid到attachmentId的映射
 * @returns 处理后的HTML内容
 */
async function processInlineImages(htmlContent: string, attachmentMap: Map<string, string>): Promise<string> {
  let processedHtml = htmlContent;

  // 处理 cid: 引用
  for (const [contentId, attachmentId] of attachmentMap) {
    // 匹配各种可能的cid引用格式
    const cidPatterns = [
      new RegExp(`cid:${contentId}`, 'gi'),
      new RegExp(`cid:"${contentId}"`, 'gi'),
      new RegExp(`cid:'${contentId}'`, 'gi'),
      new RegExp(`"cid:${contentId}"`, 'gi'),
      new RegExp(`'cid:${contentId}'`, 'gi')
    ];

    for (const pattern of cidPatterns) {
      // 替换为实际的附件URL
      processedHtml = processedHtml.replace(pattern, `/api/attachments/${attachmentId}?download=true`);
    }
  }

  return processedHtml;
}

/**
 * 将 ArrayBuffer 转换为 Base64 字符串
 * @param buffer ArrayBuffer 数据
 * @returns Base64 字符串
 */
function arrayBufferToBase64(buffer: ArrayBuffer): string {
  const bytes = new Uint8Array(buffer);
  let binary = '';
  for (let i = 0; i < bytes.byteLength; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return btoa(binary);
}